import Stripe from "stripe"

// Initialize Stripe with the secret key
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!)

// Subscription plan IDs
export const PLANS = {
  MONTHLY: process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_MONTHLY!,
  YEARLY: process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_YEARLY!,
}

// Subscription limits
export const SUBSCRIPTION_LIMITS = {
  FREE: {
    MAX_SQUADS: 1,
    MAX_TRIPS_PER_SQUAD: 2,
    MAX_DAILY_AI_REQUESTS: 10,
    MAX_WEEKLY_AI_REQUESTS: 50,
    HAS_TRIP_CHAT: false,
  },
  PRO: {
    MAX_SQUADS: 5,
    MAX_TRIPS_PER_SQUAD: 3,
    MAX_DAILY_AI_REQUESTS: Infinity,
    MAX_WEEKLY_AI_REQUESTS: Infinity,
    HAS_TRIP_CHAT: true,
  },
}

// Subscription prices (for display purposes)
export const SUBSCRIPTION_PRICES = {
  MONTHLY: 7.99,
  YEARLY: 79.99,
}

// Create a Stripe checkout session
export const createCheckoutSession = async ({
  priceId,
  userId,
  customerEmail,
  mode = "subscription",
}: {
  priceId: string
  userId: string
  customerEmail: string
  mode?: "subscription" | "payment"
}) => {
  const checkoutSession = await stripe.checkout.sessions.create({
    mode,
    payment_method_types: ["card"],
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    customer_email: customerEmail,
    client_reference_id: userId,
    success_url: `${process.env.NEXT_PUBLIC_APP_URL}/settings?tab=billing&session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/settings?tab=billing&canceled=true`,
    metadata: {
      userId,
    },
  })

  return checkoutSession
}

// Create a Stripe customer portal session
export const createCustomerPortalSession = async (customerId: string) => {
  const portalSession = await stripe.billingPortal.sessions.create({
    customer: customerId,
    return_url: `${process.env.NEXT_PUBLIC_APP_URL}/settings?tab=billing`,
  })

  return portalSession
}

// Get a Stripe customer by ID
export const getCustomer = async (customerId: string) => {
  return await stripe.customers.retrieve(customerId)
}

// Get a Stripe subscription by ID
export const getSubscription = async (subscriptionId: string) => {
  return await stripe.subscriptions.retrieve(subscriptionId)
}

// Get invoices for a customer
export const getInvoices = async (customerId: string) => {
  return await stripe.invoices.list({
    customer: customerId,
    limit: 10,
  })
}
