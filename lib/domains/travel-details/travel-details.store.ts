import { create } from "zustand"
import { immer } from "zustand/middleware/immer"
import { MemberTravelDetails, ImageUploadData } from "./travel-details.types"
import { TravelDetailsService } from "./travel-details.service"

/**
 * Travel details store state
 */
interface TravelDetailsState {
  // Data
  travelDetails: Record<string, MemberTravelDetails[]> // tripId -> travel details array
  currentTripDetails: MemberTravelDetails[]

  // Loading states
  loading: boolean
  uploadingImages: Record<string, boolean> // memberId -> uploading state

  // Error state
  error: Error | null

  // Actions
  fetchTripTravelDetails: (tripId: string) => Promise<void>
  uploadAndUpdateImage: (
    tripId: string,
    memberId: string,
    memberName: string,
    imageData: ImageUploadData
  ) => Promise<boolean>
  clearError: () => void
  setCurrentTripDetails: (details: MemberTravelDetails[]) => void
}

/**
 * Travel details store with Zustand
 */
export const useTravelDetailsStore = create<TravelDetailsState>()(
  immer((set, get) => ({
    // Initial state
    travelDetails: {},
    currentTripDetails: [],
    loading: false,
    uploadingImages: {},
    error: null,

    // Actions
    fetchTripTravelDetails: async (tripId: string) => {
      try {
        set((state) => {
          state.loading = true
          state.error = null
        })

        const details = await TravelDetailsService.getTripTravelDetails(tripId)

        set((state) => {
          state.travelDetails[tripId] = details
          state.currentTripDetails = details
          state.loading = false
        })
      } catch (error) {
        console.error("Error fetching trip travel details:", error)
        set((state) => {
          state.error = error as Error
          state.loading = false
        })
      }
    },

    uploadAndUpdateImage: async (
      tripId: string,
      memberId: string,
      memberName: string,
      imageData: ImageUploadData
    ): Promise<boolean> => {
      try {
        set((state) => {
          state.uploadingImages[memberId] = true
          state.error = null
        })

        // Upload image to Vercel Blob
        const uploadResult = await TravelDetailsService.uploadImage(tripId, memberId, imageData)

        if (!uploadResult.success || !uploadResult.url) {
          const errorMessage = uploadResult.error || "Upload failed"
          console.error("Upload failed:", errorMessage)
          throw new Error(errorMessage)
        }

        // Update Firestore with the new image URL
        const updateResult = await TravelDetailsService.updateMemberImage(
          tripId,
          memberId,
          memberName,
          imageData.type,
          uploadResult.url
        )

        if (!updateResult.success) {
          console.error("Firestore update failed")
          throw new Error("Failed to update travel details")
        }

        // Update local state
        set((state) => {
          const tripDetails = state.travelDetails[tripId] || []
          const existingIndex = tripDetails.findIndex((detail) => detail.id === memberId)

          if (existingIndex >= 0) {
            // Update existing member
            if (imageData.type === "flight") {
              tripDetails[existingIndex].flightImage = uploadResult.url
            } else {
              tripDetails[existingIndex].accommodationImage = uploadResult.url
            }
          } else {
            // Add new member
            const newDetail: MemberTravelDetails = {
              id: memberId,
              memberName,
              createdAt: null,
              lastUpdated: null as any, // Will be set by server
              [imageData.type === "flight" ? "flightImage" : "accommodationImage"]:
                uploadResult.url,
            }
            tripDetails.push(newDetail)
          }

          state.travelDetails[tripId] = tripDetails
          state.currentTripDetails = tripDetails
          state.uploadingImages[memberId] = false
        })

        return true
      } catch (error) {
        console.error("Error uploading and updating image:", {
          error,
          message: error instanceof Error ? error.message : "Unknown error",
          stack: error instanceof Error ? error.stack : undefined,
          tripId,
          memberId,
          memberName,
          imageType: imageData.type,
        })

        set((state) => {
          state.error = error as Error
          state.uploadingImages[memberId] = false
        })
        return false
      }
    },

    clearError: () => {
      set((state) => {
        state.error = null
      })
    },

    setCurrentTripDetails: (details: MemberTravelDetails[]) => {
      set((state) => {
        state.currentTripDetails = details
      })
    },
  }))
)
