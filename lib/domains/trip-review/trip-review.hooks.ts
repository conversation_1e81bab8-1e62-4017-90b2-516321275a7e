"use client"

import { useCallback } from "react"
import { useUser } from "../auth/auth.hooks"
import { TripReviewService } from "./trip-review.service"
import { TripReviewCreateData, TripReviewFormData, TRIP_REVIEW_CONSTRAINTS } from "./trip-review.types"
import {
  useTripReviewStore,
  selectTripReviews,
  selectUserReview,
  selectTripAggregate,
  selectIsLoadingReviews,
} from "./trip-review.store"

/**
 * Hook to get trip reviews
 */
export const useTripReviews = (tripId: string) => {
  const reviews = useTripReviewStore(selectTripReviews(tripId))
  const loading = useTripReviewStore(selectIsLoadingReviews(tripId))
  const error = useTripReviewStore((state) => state.error)

  return { reviews, loading, error }
}

/**
 * Hook to get user's review for a trip
 */
export const useUserTripReview = (tripId: string) => {
  const userReview = useTripReviewStore(selectUserReview(tripId))
  const loading = useTripReviewStore((state) => state.loading)
  const error = useTripReviewStore((state) => state.error)

  return { userReview, loading, error }
}

/**
 * Hook to get trip review aggregate data
 */
export const useTripReviewAggregate = (tripId: string) => {
  const aggregate = useTripReviewStore(selectTripAggregate(tripId))
  const loading = useTripReviewStore((state) => state.loading)
  const error = useTripReviewStore((state) => state.error)

  return { aggregate, loading, error }
}

/**
 * Hook to create a trip review
 */
export const useCreateTripReview = () => {
  const { user } = useUser()
  const submitting = useTripReviewStore((state) => state.submitting)
  const submitError = useTripReviewStore((state) => state.submitError)
  const { setSubmitting, setSubmitError, addReview, setUserReview } = useTripReviewStore()

  const createReview = useCallback(
    async (tripId: string, formData: TripReviewFormData) => {
      if (!user) {
        throw new Error("User must be authenticated to create a review")
      }

      // Validate form data
      if (formData.rating < TRIP_REVIEW_CONSTRAINTS.RATING_MIN || 
          formData.rating > TRIP_REVIEW_CONSTRAINTS.RATING_MAX) {
        throw new Error(`Rating must be between ${TRIP_REVIEW_CONSTRAINTS.RATING_MIN} and ${TRIP_REVIEW_CONSTRAINTS.RATING_MAX}`)
      }

      if (formData.feedback.length < TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MIN_LENGTH) {
        throw new Error(`Feedback must be at least ${TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MIN_LENGTH} characters`)
      }

      if (formData.feedback.length > TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MAX_LENGTH) {
        throw new Error(`Feedback must be no more than ${TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MAX_LENGTH} characters`)
      }

      setSubmitting(true)
      setSubmitError(null)

      try {
        // Check if user has already reviewed this trip
        const existingReview = await TripReviewService.hasUserReviewed(tripId, user.uid)
        if (existingReview) {
          throw new Error("You have already reviewed this trip")
        }

        const reviewData: TripReviewCreateData = {
          tripId,
          userId: user.uid,
          rating: formData.rating,
          feedback: formData.feedback.trim(),
          userDisplayName: user.displayName,
          userPhotoURL: user.photoURL,
        }

        const reviewId = await TripReviewService.createReview(tripId, reviewData)

        // Create the full review object for store update
        const newReview = {
          ...reviewData,
          id: reviewId,
          createdAt: new Date() as any, // Will be replaced by server timestamp
          reviewDate: new Date() as any, // Will be replaced by server timestamp
        }

        // Update store
        addReview(tripId, newReview)
        setUserReview(tripId, newReview)

        return reviewId
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to create review"
        setSubmitError(new Error(errorMessage))
        throw error
      } finally {
        setSubmitting(false)
      }
    },
    [user, setSubmitting, setSubmitError, addReview, setUserReview]
  )

  return { createReview, submitting, submitError }
}

/**
 * Hook to load trip reviews
 */
export const useLoadTripReviews = () => {
  const { setReviews, setLoadingReviews, setError } = useTripReviewStore()

  const loadReviews = useCallback(
    async (tripId: string) => {
      setLoadingReviews(tripId, true)
      setError(null)

      try {
        const reviews = await TripReviewService.getTripReviews(tripId)
        setReviews(tripId, reviews)
        return reviews
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to load reviews"
        setError(new Error(errorMessage))
        throw error
      } finally {
        setLoadingReviews(tripId, false)
      }
    },
    [setReviews, setLoadingReviews, setError]
  )

  return { loadReviews }
}

/**
 * Hook to load user's review for a trip
 */
export const useLoadUserTripReview = () => {
  const { user } = useUser()
  const { setUserReview, setLoading, setError } = useTripReviewStore()

  const loadUserReview = useCallback(
    async (tripId: string) => {
      if (!user) {
        setUserReview(tripId, null)
        return null
      }

      setLoading(true)
      setError(null)

      try {
        const review = await TripReviewService.getUserReview(tripId, user.uid)
        setUserReview(tripId, review)
        return review
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to load user review"
        setError(new Error(errorMessage))
        throw error
      } finally {
        setLoading(false)
      }
    },
    [user, setUserReview, setLoading, setError]
  )

  return { loadUserReview }
}

/**
 * Hook to load trip review aggregate
 */
export const useLoadTripReviewAggregate = () => {
  const { setAggregate, setLoading, setError } = useTripReviewStore()

  const loadAggregate = useCallback(
    async (tripId: string) => {
      setLoading(true)
      setError(null)

      try {
        const aggregate = await TripReviewService.getTripReviewAggregate(tripId)
        setAggregate(tripId, aggregate)
        return aggregate
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to load review aggregate"
        setError(new Error(errorMessage))
        throw error
      } finally {
        setLoading(false)
      }
    },
    [setAggregate, setLoading, setError]
  )

  return { loadAggregate }
}
