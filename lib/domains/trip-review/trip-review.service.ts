import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  serverTimestamp,
  Timestamp,
} from "firebase/firestore"
import { db } from "@/lib/firebase"
import { TripReview, TripReviewCreateData, TripReviewAggregate } from "./trip-review.types"

/**
 * Trip review service for Firebase operations
 */
export class TripReviewService {
  private static readonly TRIPS_COLLECTION = "trips"
  private static readonly REVIEWS_SUBCOLLECTION = "reviews"

  /**
   * Create a new trip review
   * @param tripId Trip ID
   * @param reviewData Review data
   * @returns The new review ID
   */
  static async createReview(tripId: string, reviewData: TripReviewCreateData): Promise<string> {
    try {
      const reviewRef = doc(
        collection(db, this.TRIPS_COLLECTION, tripId, this.REVIEWS_SUBCOLLECTION)
      )
      const reviewId = reviewRef.id

      await setDoc(reviewRef, {
        ...reviewData,
        id: reviewId,
        tripId,
        reviewDate: serverTimestamp(),
        createdAt: serverTimestamp(),
      })

      return reviewId
    } catch (error) {
      console.error("Error creating trip review:", error)
      throw error
    }
  }

  /**
   * Get a specific trip review by user
   * @param tripId Trip ID
   * @param userId User ID
   * @returns The review or null if not found
   */
  static async getUserReview(tripId: string, userId: string): Promise<TripReview | null> {
    try {
      const reviewsRef = collection(db, this.TRIPS_COLLECTION, tripId, this.REVIEWS_SUBCOLLECTION)
      const q = query(reviewsRef, where("userId", "==", userId))
      const querySnapshot = await getDocs(q)

      if (querySnapshot.empty) {
        return null
      }

      const doc = querySnapshot.docs[0]
      return { ...doc.data(), id: doc.id } as TripReview
    } catch (error) {
      console.error("Error getting user review:", error)
      throw error
    }
  }

  /**
   * Get all reviews for a trip
   * @param tripId Trip ID
   * @returns Array of reviews
   */
  static async getTripReviews(tripId: string): Promise<TripReview[]> {
    try {
      const reviewsRef = collection(db, this.TRIPS_COLLECTION, tripId, this.REVIEWS_SUBCOLLECTION)
      const q = query(reviewsRef, orderBy("reviewDate", "desc"))
      const querySnapshot = await getDocs(q)

      return querySnapshot.docs.map((doc) => ({
        ...doc.data(),
        id: doc.id,
      })) as TripReview[]
    } catch (error) {
      console.error("Error getting trip reviews:", error)
      throw error
    }
  }

  /**
   * Calculate review aggregate data for a trip
   * @param tripId Trip ID
   * @returns Aggregate review data
   */
  static async getTripReviewAggregate(tripId: string): Promise<TripReviewAggregate> {
    try {
      const reviews = await this.getTripReviews(tripId)

      if (reviews.length === 0) {
        return {
          tripId,
          averageRating: 0,
          totalReviews: 0,
          ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
        }
      }

      const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0)
      const averageRating = totalRating / reviews.length

      const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
      reviews.forEach((review) => {
        ratingDistribution[review.rating as keyof typeof ratingDistribution]++
      })

      return {
        tripId,
        averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
        totalReviews: reviews.length,
        ratingDistribution,
      }
    } catch (error) {
      console.error("Error calculating trip review aggregate:", error)
      throw error
    }
  }

  /**
   * Check if a user has already reviewed a trip
   * @param tripId Trip ID
   * @param userId User ID
   * @returns True if user has already reviewed
   */
  static async hasUserReviewed(tripId: string, userId: string): Promise<boolean> {
    try {
      const review = await this.getUserReview(tripId, userId)
      return review !== null
    } catch (error) {
      console.error("Error checking if user has reviewed:", error)
      throw error
    }
  }

  /**
   * Get review counts for multiple trips
   * @param tripIds Array of trip IDs
   * @returns Map of trip ID to review count
   */
  static async getTripsReviewCounts(tripIds: string[]): Promise<Map<string, number>> {
    try {
      const reviewCounts = new Map<string, number>()

      await Promise.all(
        tripIds.map(async (tripId) => {
          const reviews = await this.getTripReviews(tripId)
          reviewCounts.set(tripId, reviews.length)
        })
      )

      return reviewCounts
    } catch (error) {
      console.error("Error getting trips review counts:", error)
      throw error
    }
  }
}
