"use client"

import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import { UserSubscription } from "./user-subscription.types"
import { UserSubscriptionService } from "./user-subscription.service"
import { SUBSCRIPTION_LIMITS } from "@/lib/stripe-client"
import { AI_USAGE_LIMITS } from "@/lib/firebase/ai-usage-service"
import { SubscriptionErrorType } from "@/lib/subscription-errors"
import { toast } from "@/components/ui/use-toast"

/**
 * User subscription store state interface
 */
interface UserSubscriptionState {
  // State
  subscription: UserSubscription | null
  loading: boolean
  error: Error | null
  isSubscribed: boolean
  subscriptionPlan: "free" | "monthly" | "yearly" | null
  subscriptionStatus: "active" | "canceled" | "past_due" | "trialing" | "incomplete" | null
  lastAuthRefresh: number

  // Limits
  maxSquads: number
  maxTripsPerSquad: number
  maxDailyAIRequests: number
  maxWeeklyAIRequests: number
  hasTripChat: boolean

  // Actions
  setSubscription: (subscription: UserSubscription | null) => void
  setLoading: (loading: boolean) => void
  fetchSubscription: (userId: string) => Promise<void>
  refreshSubscriptionIfNeeded: (userId: string) => void
  canCreateMoreSquads: (userId: string, currentSquadCount?: number) => Promise<boolean>
  canCreateMoreTripsInSquad: (
    userId: string,
    squadId: string,
    currentTripCount?: number
  ) => Promise<boolean>
  handleSubscriptionError: (errorType: SubscriptionErrorType) => void
  handleSubscriptionErrorWithRouter: (errorType: SubscriptionErrorType, router: any) => void
}

// Create a custom storage object that only uses localStorage on the client side
const customStorage = {
  getItem: (name: string) => {
    if (typeof window === "undefined") return null
    return window.localStorage.getItem(name)
  },
  setItem: (name: string, value: string) => {
    if (typeof window !== "undefined") {
      window.localStorage.setItem(name, value)
    }
  },
  removeItem: (name: string) => {
    if (typeof window !== "undefined") {
      window.localStorage.removeItem(name)
    }
  },
}

/**
 * User subscription store with Zustand
 */
export const useUserSubscriptionStore = create<UserSubscriptionState>()(
  persist(
    (set, get) => ({
      // Initial state
      subscription: null,
      loading: true,
      error: null,
      isSubscribed: false,
      subscriptionPlan: "free",
      subscriptionStatus: null,
      lastAuthRefresh: 0,

      // Default limits
      maxSquads: SUBSCRIPTION_LIMITS.FREE.MAX_SQUADS,
      maxTripsPerSquad: SUBSCRIPTION_LIMITS.FREE.MAX_TRIPS_PER_SQUAD,
      maxDailyAIRequests: AI_USAGE_LIMITS.FREE.DAILY,
      maxWeeklyAIRequests: AI_USAGE_LIMITS.FREE.WEEKLY,
      hasTripChat: SUBSCRIPTION_LIMITS.FREE.HAS_TRIP_CHAT,

      // Actions
      setSubscription: (subscription) => {
        const isSubscribed =
          subscription?.subscriptionStatus === "active" ||
          subscription?.subscriptionStatus === "trialing"

        // Determine limits based on subscription status
        const maxSquads = isSubscribed
          ? SUBSCRIPTION_LIMITS.PRO.MAX_SQUADS
          : SUBSCRIPTION_LIMITS.FREE.MAX_SQUADS

        const maxTripsPerSquad = isSubscribed
          ? SUBSCRIPTION_LIMITS.PRO.MAX_TRIPS_PER_SQUAD
          : SUBSCRIPTION_LIMITS.FREE.MAX_TRIPS_PER_SQUAD

        const maxDailyAIRequests = isSubscribed
          ? AI_USAGE_LIMITS.PRO.DAILY
          : AI_USAGE_LIMITS.FREE.DAILY

        const maxWeeklyAIRequests = isSubscribed
          ? AI_USAGE_LIMITS.PRO.WEEKLY
          : AI_USAGE_LIMITS.FREE.WEEKLY

        const hasTripChat = isSubscribed
          ? SUBSCRIPTION_LIMITS.PRO.HAS_TRIP_CHAT
          : SUBSCRIPTION_LIMITS.FREE.HAS_TRIP_CHAT

        set({
          subscription,
          isSubscribed,
          subscriptionPlan: subscription?.subscriptionPlan || "free",
          subscriptionStatus: subscription?.subscriptionStatus || null,
          maxSquads,
          maxTripsPerSquad,
          maxDailyAIRequests,
          maxWeeklyAIRequests,
          hasTripChat,
        })
      },

      setLoading: (loading) => set({ loading }),

      // Fetch subscription data
      fetchSubscription: async (userId: string) => {
        if (!userId) {
          set({
            loading: false,
            subscription: null,
            isSubscribed: false,
            subscriptionPlan: "free",
            subscriptionStatus: null,
            maxSquads: SUBSCRIPTION_LIMITS.FREE.MAX_SQUADS,
            maxTripsPerSquad: SUBSCRIPTION_LIMITS.FREE.MAX_TRIPS_PER_SQUAD,
            maxDailyAIRequests: AI_USAGE_LIMITS.FREE.DAILY,
            maxWeeklyAIRequests: AI_USAGE_LIMITS.FREE.WEEKLY,
            hasTripChat: SUBSCRIPTION_LIMITS.FREE.HAS_TRIP_CHAT,
            lastAuthRefresh: 0,
          })
          return
        }

        try {
          set({ loading: true, error: null })
          const subscription = await UserSubscriptionService.getUserSubscription(userId)

          // Update the last refresh timestamp
          set({ lastAuthRefresh: Date.now() })

          // Set the subscription data
          get().setSubscription(subscription)
          set({ loading: false })
        } catch (error) {
          console.error("Error fetching subscription:", error)
          set({
            error: error as Error,
            loading: false,
            subscription: null,
            isSubscribed: false,
            subscriptionPlan: "free",
            subscriptionStatus: null,
          })
        }
      },

      // Refresh subscription if needed (e.g., after a long session)
      refreshSubscriptionIfNeeded: (userId: string) => {
        const { lastAuthRefresh } = get()
        const now = Date.now()
        const refreshInterval = 30 * 60 * 1000 // 30 minutes

        // Refresh if it's been more than the refresh interval
        if (now - lastAuthRefresh > refreshInterval) {
          get().fetchSubscription(userId)
        }
      },

      // Check if a user can create more squads
      canCreateMoreSquads: async (userId: string, currentSquadCount?: number) => {
        try {
          const { maxSquads } = get()

          // If we have infinite squads, return true
          if (maxSquads === Infinity) return true

          // If we already have the count, use it
          if (typeof currentSquadCount === "number") {
            return currentSquadCount < maxSquads
          }

          // Otherwise, fetch the current count using the SquadService
          const { SquadService } = await import("@/lib/domains/squad/squad.service")
          const userSquads = await SquadService.getUserSquads(userId)
          return userSquads.length < maxSquads
        } catch (error) {
          console.error("Error checking if user can create more squads:", error)
          return false
        }
      },

      // Check if a user can create more trips in a squad
      canCreateMoreTripsInSquad: async (
        userId: string,
        squadId: string,
        currentTripCount?: number
      ) => {
        try {
          const { maxTripsPerSquad } = get()

          // If we have infinite trips, return true
          if (maxTripsPerSquad === Infinity) return true

          // If we already have the count, use it
          if (typeof currentTripCount === "number") {
            return currentTripCount < maxTripsPerSquad
          }

          // Otherwise, fetch the current count using the TripService
          const { TripService } = await import("@/lib/domains/trip/trip.service")
          const squadTrips = await TripService.getSquadTrips(squadId)

          // Only count non-completed trips (planning, upcoming, active)
          const activeTrips = squadTrips.filter(
            (trip) =>
              trip.status === "planning" || trip.status === "upcoming" || trip.status === "active"
          )

          return activeTrips.length < maxTripsPerSquad
        } catch (error) {
          console.error("Error checking if user can create more trips in squad:", error)
          return false
        }
      },

      // Handle subscription errors
      handleSubscriptionError: (errorType: SubscriptionErrorType) => {
        // Import error messages
        const { SUBSCRIPTION_ERROR_MESSAGES } = require("@/lib/subscription-errors")

        // Show toast with error message
        toast({
          title: SUBSCRIPTION_ERROR_MESSAGES[errorType].title,
          description: SUBSCRIPTION_ERROR_MESSAGES[errorType].description,
          variant: "destructive",
        })
      },

      // Handle subscription errors with router navigation
      handleSubscriptionErrorWithRouter: (errorType: SubscriptionErrorType, router: any) => {
        // First use the regular error handler
        get().handleSubscriptionError(errorType)

        // If the error requires navigation, use the router
        if (
          errorType === SubscriptionErrorType.MAX_SQUADS_REACHED ||
          errorType === SubscriptionErrorType.MAX_TRIPS_PER_SQUAD_REACHED ||
          errorType === SubscriptionErrorType.DAILY_AI_LIMIT_REACHED ||
          errorType === SubscriptionErrorType.WEEKLY_AI_LIMIT_REACHED
        ) {
          router.push("/settings?tab=billing")
        }
      },
    }),
    {
      name: "brotrips-user-subscription-storage",
      storage: createJSONStorage(() => customStorage),
      skipHydration: true, // Skip hydration to prevent hydration mismatch
      partialize: (state) => ({
        // Only persist these fields
        subscriptionPlan: state.subscriptionPlan,
        subscriptionStatus: state.subscriptionStatus,
      }),
    }
  )
)
