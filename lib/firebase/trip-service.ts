import { db } from "../firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  type Timestamp,
  setDoc,
} from "firebase/firestore"
import { getTripAttendees } from "./user-trip-service"
import { normalizeToUTCMidnight } from "../utils/date-utils"

// Types
export interface Trip {
  id: string
  name: string
  destination: string
  squadId: string
  startDate: Timestamp
  endDate: Timestamp
  budget: string
  description?: string
  image?: string
  locationThumbnail?: string
  imageAttribution?: {
    name: string
    photoReference?: string
    username?: string
    link?: string
  }
  status: "planning" | "upcoming" | "active" | "completed" | "cancelled"
  attendees: string[] // Array of user IDs with "going" status - should be kept in sync with userTrips collection
  leaderId: string // User ID of the trip leader
  tasksCompleted: number
  totalTasks: number
  createdBy: string // User ID
  createdAt: Timestamp | null
}

// Trip operations
export const createTrip = async (
  tripData: Omit<Trip, "id" | "createdAt" | "tasksCompleted" | "totalTasks">
) => {
  try {
    const tripRef = doc(collection(db, "trips"))
    const tripId = tripRef.id

    // Normalize dates to UTC midnight to ensure consistent storage regardless of user timezone
    const normalizedData = {
      ...tripData,
      startDate: normalizeToUTCMidnight(tripData.startDate.toDate()) as any,
      endDate: normalizeToUTCMidnight(tripData.endDate.toDate()) as any,
    }

    await setDoc(tripRef, {
      ...normalizedData,
      id: tripId,
      tasksCompleted: 0,
      totalTasks: 0,
      createdAt: serverTimestamp(),
    })

    return tripId
  } catch (error) {
    console.error("Error creating trip:", error)
    throw error
  }
}

export const getTrip = async (tripId: string) => {
  try {
    const tripDoc = await getDoc(doc(db, "trips", tripId))
    if (tripDoc.exists()) {
      const data = tripDoc.data()

      // Get the actual attendees from userTrips collection
      const attendees = await getTripAttendees(tripId)

      return { ...data, id: tripId, attendees } as Trip
    }
    return null
  } catch (error) {
    console.error("Error getting trip:", error)
    throw error
  }
}

export const getUserTrips = async (userId: string) => {
  try {
    // Query trips where the user is in the attendees array
    // Note: This is kept for backward compatibility, but the source of truth for attendees
    // should be the userTrips collection with status="going"
    const q = query(collection(db, "trips"), where("attendees", "array-contains", userId))
    const querySnapshot = await getDocs(q)

    // Process each trip and ensure the attendees field is up-to-date
    const trips = await Promise.all(
      querySnapshot.docs.map(async (doc) => {
        const data = doc.data()
        const tripId = doc.id

        // Get the actual attendees from userTrips collection
        const attendees = await getTripAttendees(tripId)

        // Ensure tasksCompleted and totalTasks have default values
        const tasksCompleted = typeof data.tasksCompleted === "number" ? data.tasksCompleted : 0
        const totalTasks = typeof data.totalTasks === "number" ? data.totalTasks : 0

        return {
          ...data,
          id: tripId,
          attendees,
          tasksCompleted,
          totalTasks,
        } as Trip
      })
    )

    return trips
  } catch (error) {
    console.error("Error getting user trips:", error)
    throw error
  }
}

export const getSquadTrips = async (squadId: string) => {
  try {
    const q = query(collection(db, "trips"), where("squadId", "==", squadId))
    const querySnapshot = await getDocs(q)

    // Process each trip and ensure the attendees field is up-to-date
    const trips = await Promise.all(
      querySnapshot.docs.map(async (doc) => {
        const data = doc.data()
        const tripId = doc.id

        // Get the actual attendees from userTrips collection
        const attendees = await getTripAttendees(tripId)

        // Ensure tasksCompleted and totalTasks have default values
        const tasksCompleted = typeof data.tasksCompleted === "number" ? data.tasksCompleted : 0
        const totalTasks = typeof data.totalTasks === "number" ? data.totalTasks : 0

        return {
          ...data,
          id: tripId,
          attendees,
          tasksCompleted,
          totalTasks,
        } as Trip
      })
    )

    return trips
  } catch (error) {
    console.error("Error getting squad trips:", error)
    throw error
  }
}

export const updateTrip = async (tripId: string, tripData: Partial<Omit<Trip, "destination">>) => {
  try {
    // Normalize dates to UTC midnight if they are being updated
    const normalizedData = { ...tripData }

    if (normalizedData.startDate) {
      normalizedData.startDate = normalizeToUTCMidnight(normalizedData.startDate.toDate()) as any
    }

    if (normalizedData.endDate) {
      normalizedData.endDate = normalizeToUTCMidnight(normalizedData.endDate.toDate()) as any
    }

    await updateDoc(doc(db, "trips", tripId), normalizedData)
    return { success: true }
  } catch (error) {
    console.error("Error updating trip:", error)
    throw error
  }
}

// Check if a user is the trip leader
export const isUserTripLeader = async (userId: string, tripId: string) => {
  try {
    const trip = await getTrip(tripId)
    return trip?.leaderId === userId
  } catch (error) {
    console.error("Error checking if user is trip leader:", error)
    return false
  }
}
