"use client"

import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import { useAuthStore } from "./auth-store"
import { SUBSCRIPTION_LIMITS } from "@/lib/stripe-client"
import { SquadService } from "@/lib/domains/squad/squad.service"
import { getSquadTrips } from "@/lib/firebase/trip-service"
import { SubscriptionErrorType, handleSubscriptionError } from "@/lib/subscription-errors"
import { AI_USAGE_LIMITS } from "@/lib/domains/user-ai-usage/user-ai-usage.types"
import { UserAIUsageService } from "@/lib/domains/user-ai-usage/user-ai-usage.service"
import { getUserSubscription, type UserSubscription } from "@/lib/firebase/subscription-service"

// Define the subscription store state
interface SubscriptionState {
  // State
  subscription: UserSubscription | null
  loading: boolean
  isSubscribed: boolean
  subscriptionPlan: "free" | "monthly" | "yearly" | null
  subscriptionStatus: "active" | "canceled" | "past_due" | "trialing" | "incomplete" | null
  lastAuthRefresh: number

  // Limits
  maxSquads: number
  maxTripsPerSquad: number
  maxDailyAIRequests: number
  maxWeeklyAIRequests: number
  hasTripChat: boolean

  // Actions
  setSubscription: (subscription: UserSubscription | null) => void
  setLoading: (loading: boolean) => void
  fetchSubscription: () => Promise<void>
  refreshSubscriptionIfNeeded: () => void
  canCreateMoreSquads: (currentSquadCount?: number) => Promise<boolean>
  canCreateMoreTripsInSquad: (squadId: string, currentTripCount?: number) => Promise<boolean>
  handleSubscriptionError: (errorType: SubscriptionErrorType) => void
  handleSubscriptionErrorWithRouter: (errorType: SubscriptionErrorType, router: any) => void
  canMakeAIRequest: () => Promise<boolean>
  incrementAIUsage: () => Promise<void>
  getAIUsage: () => Promise<{
    daily: number
    weekly: number
    dailyLimit: number
    weeklyLimit: number
    canMakeRequest: boolean
  }>
}

// Create a custom storage object that only uses localStorage on the client side
const customStorage = {
  getItem: (name: string) => {
    if (typeof window === "undefined") return null
    return window.localStorage.getItem(name)
  },
  setItem: (name: string, value: string) => {
    if (typeof window !== "undefined") {
      window.localStorage.setItem(name, value)
    }
  },
  removeItem: (name: string) => {
    if (typeof window !== "undefined") {
      window.localStorage.removeItem(name)
    }
  },
}

// Create the subscription store
export const useSubscriptionStore = create<SubscriptionState>()(
  persist(
    (set, get) => ({
      // Initial state
      subscription: null,
      loading: true,
      isSubscribed: false,
      subscriptionPlan: "free",
      subscriptionStatus: null,
      lastAuthRefresh: 0,

      // Default limits
      maxSquads: SUBSCRIPTION_LIMITS.FREE.MAX_SQUADS,
      maxTripsPerSquad: SUBSCRIPTION_LIMITS.FREE.MAX_TRIPS_PER_SQUAD,
      maxDailyAIRequests: AI_USAGE_LIMITS.FREE.DAILY,
      maxWeeklyAIRequests: AI_USAGE_LIMITS.FREE.WEEKLY,
      hasTripChat: SUBSCRIPTION_LIMITS.FREE.HAS_TRIP_CHAT,

      // Actions
      setSubscription: (subscription) => {
        const isSubscribed =
          subscription?.subscriptionStatus === "active" ||
          subscription?.subscriptionStatus === "trialing"

        // Determine limits based on subscription status
        const maxSquads = isSubscribed
          ? SUBSCRIPTION_LIMITS.PRO.MAX_SQUADS
          : SUBSCRIPTION_LIMITS.FREE.MAX_SQUADS

        const maxTripsPerSquad = isSubscribed
          ? SUBSCRIPTION_LIMITS.PRO.MAX_TRIPS_PER_SQUAD
          : SUBSCRIPTION_LIMITS.FREE.MAX_TRIPS_PER_SQUAD

        const maxDailyAIRequests = isSubscribed
          ? AI_USAGE_LIMITS.PRO.DAILY
          : AI_USAGE_LIMITS.FREE.DAILY

        const maxWeeklyAIRequests = isSubscribed
          ? AI_USAGE_LIMITS.PRO.WEEKLY
          : AI_USAGE_LIMITS.FREE.WEEKLY

        const hasTripChat = isSubscribed
          ? SUBSCRIPTION_LIMITS.PRO.HAS_TRIP_CHAT
          : SUBSCRIPTION_LIMITS.FREE.HAS_TRIP_CHAT

        set({
          subscription,
          isSubscribed,
          subscriptionPlan: subscription?.subscriptionPlan || "free",
          subscriptionStatus: subscription?.subscriptionStatus || null,
          maxSquads,
          maxTripsPerSquad,
          maxDailyAIRequests,
          maxWeeklyAIRequests,
          hasTripChat,
        })
      },
      setLoading: (loading) => set({ loading }),

      // Fetch subscription data
      fetchSubscription: async () => {
        const { user } = useAuthStore.getState()
        if (!user?.uid) {
          set({
            loading: false,
            subscription: null,
            isSubscribed: false,
            subscriptionPlan: "free",
            subscriptionStatus: null,
            maxSquads: SUBSCRIPTION_LIMITS.FREE.MAX_SQUADS,
            maxTripsPerSquad: SUBSCRIPTION_LIMITS.FREE.MAX_TRIPS_PER_SQUAD,
            maxDailyAIRequests: AI_USAGE_LIMITS.FREE.DAILY,
            maxWeeklyAIRequests: AI_USAGE_LIMITS.FREE.WEEKLY,
            hasTripChat: SUBSCRIPTION_LIMITS.FREE.HAS_TRIP_CHAT,
            lastAuthRefresh: 0,
          })
          return
        }

        try {
          set({ loading: true })

          // Get the user subscription from Firebase
          const userSubscription = await getUserSubscription(user.uid)

          // Update the store
          get().setSubscription(userSubscription)

          set({ loading: false })
        } catch (error) {
          console.error("Error fetching user subscription:", error)
          set({ loading: false })
        }
      },

      // Check if user can create more squads
      canCreateMoreSquads: async (currentSquadCount) => {
        const { user } = useAuthStore.getState()
        const { maxSquads } = get()

        if (!user?.uid) return false

        try {
          // If we already have the count, use it
          if (currentSquadCount !== undefined) {
            return currentSquadCount < maxSquads
          }

          // Otherwise, fetch the count from Firebase
          const userSquads = await SquadService.getUserSquads(user.uid)
          return userSquads.length < maxSquads
        } catch (error) {
          console.error("Error checking if user can create more squads:", error)
          return false
        }
      },

      // Check if user can create more trips in a squad
      canCreateMoreTripsInSquad: async (squadId, currentTripCount) => {
        const { maxTripsPerSquad } = get()

        try {
          // If we already have the count, use it
          if (currentTripCount !== undefined) {
            return currentTripCount < maxTripsPerSquad
          }

          // Otherwise, fetch the count from Firebase
          const squadTrips = await getSquadTrips(squadId)

          // Only count non-completed trips (planning, upcoming, active)
          const activeTrips = squadTrips.filter(
            (trip) =>
              trip.status === "planning" || trip.status === "upcoming" || trip.status === "active"
          )

          return activeTrips.length < maxTripsPerSquad
        } catch (error) {
          console.error("Error checking if user can create more trips in squad:", error)
          return false
        }
      },

      // Handle subscription errors
      handleSubscriptionError: (errorType) => {
        // Use the existing error handler
        handleSubscriptionError(errorType)
      },

      // Check if user can make an AI request
      canMakeAIRequest: async () => {
        const { user } = useAuthStore.getState()
        const { isSubscribed } = get()
        if (!user?.uid) return false

        return UserAIUsageService.canMakeAIRequest(user.uid, isSubscribed)
      },

      // Increment AI usage and update local state
      incrementAIUsage: async () => {
        const { user } = useAuthStore.getState()
        if (!user?.uid) return

        await UserAIUsageService.incrementAIUsage(user.uid)

        // Immediately fetch updated usage data to keep UI in sync
        const { isSubscribed } = get()
        await UserAIUsageService.getAIUsage(user.uid, isSubscribed)
        // No need to update store state as getAIUsage will return the latest values
        // This ensures the next call to getAIUsage will have fresh data
      },

      // Get AI usage
      getAIUsage: async () => {
        const { user } = useAuthStore.getState()
        const { maxDailyAIRequests, maxWeeklyAIRequests, isSubscribed } = get()

        if (!user?.uid) {
          return {
            daily: 0,
            weekly: 0,
            dailyLimit: maxDailyAIRequests,
            weeklyLimit: maxWeeklyAIRequests,
            canMakeRequest: false,
          }
        }

        const usage = await UserAIUsageService.getAIUsage(user.uid, isSubscribed)

        return {
          ...usage,
          dailyLimit: maxDailyAIRequests,
          weeklyLimit: maxWeeklyAIRequests,
        }
      },

      // Refresh subscription if needed (for long sessions)
      refreshSubscriptionIfNeeded: () => {
        const now = Date.now()
        const { lastAuthRefresh } = get()
        const { refreshAdminStatus } = useAuthStore.getState()

        // If it's been more than a day, refresh auth data
        if (now - lastAuthRefresh > 24 * 60 * 60 * 1000) {
          refreshAdminStatus()
          set({ lastAuthRefresh: now })

          if (typeof window !== "undefined") {
            localStorage.setItem("last_auth_refresh", now.toString())
          }
        }
      },

      // Handle subscription errors with router navigation
      handleSubscriptionErrorWithRouter: (errorType, router) => {
        // First use the regular error handler
        get().handleSubscriptionError(errorType)

        // If the error requires navigation, use the router
        if (
          errorType === SubscriptionErrorType.MAX_SQUADS_REACHED ||
          errorType === SubscriptionErrorType.MAX_TRIPS_PER_SQUAD_REACHED ||
          errorType === SubscriptionErrorType.DAILY_AI_LIMIT_REACHED ||
          errorType === SubscriptionErrorType.WEEKLY_AI_LIMIT_REACHED
        ) {
          router.push("/settings?tab=billing")
        }
      },
    }),
    {
      name: "brotrips-subscription-storage",
      storage: createJSONStorage(() => customStorage),
      skipHydration: true, // Skip hydration to prevent hydration mismatch
      partialize: (state) => ({
        // Only persist these fields
        subscriptionPlan: state.subscriptionPlan,
        subscriptionStatus: state.subscriptionStatus,
      }),
    }
  )
)
