"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Plus, HelpCircle } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { formatDateRange } from "./utils"
import { Squad } from "@/lib/domains/squad/squad.types"
import { Trip } from "@/lib/domains/trip/trip.types"

interface SquadsTabProps {
  squads: Squad[]
  upcomingTrips: Trip[]
  loading: boolean
}

export function SquadsTab({ squads, upcomingTrips, loading }: SquadsTabProps) {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <h2 className="text-xl font-semibold">Your Squads</h2>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                aria-label="Squad information"
              >
                <HelpCircle className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-3" align="start">
              <p className="text-sm">
                Squads aren't just for friend groups, add your partner, family, or travel buddies
                and make every trip easier to plan.
              </p>
            </PopoverContent>
          </Popover>
        </div>
        <Link href="/squads/create">
          <Button>
            <Plus className="mr-2 h-4 w-4" /> Create Squad
          </Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {squads.map((squad) => (
          <Link href={`/squads/${squad.id}`} key={squad.id}>
            <Card className="h-full hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <CardTitle>{squad.name}</CardTitle>
                <CardDescription>{squad.memberCount || 0} members</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex -space-x-2 overflow-hidden mb-4">
                  {/* We'll just show placeholders for now */}
                  {[...Array(Math.min(4, squad.memberCount || 0))].map((_, i) => (
                    <Avatar key={i} className="border-2 border-background">
                      <AvatarFallback>{i + 1}</AvatarFallback>
                    </Avatar>
                  ))}
                  {(squad.memberCount || 0) > 4 && (
                    <div className="flex items-center justify-center w-10 h-10 rounded-full border-2 border-background bg-muted text-xs">
                      +{(squad.memberCount || 0) - 4}
                    </div>
                  )}
                </div>

                {/* Find upcoming trip for this squad */}
                {upcomingTrips.find((trip) => trip.squadId === squad.id) ? (
                  <div className="text-sm">
                    <Badge variant="outline" className="mb-2">
                      Upcoming Trip
                    </Badge>
                    <p className="font-medium">
                      {upcomingTrips.find((trip) => trip.squadId === squad.id)?.destination}
                    </p>
                    <p className="text-muted-foreground">
                      {formatDateRange(
                        upcomingTrips.find((trip) => trip.squadId === squad.id)?.startDate,
                        upcomingTrips.find((trip) => trip.squadId === squad.id)?.endDate
                      )}
                    </p>
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">No upcoming trips</p>
                )}
              </CardContent>
            </Card>
          </Link>
        ))}

        <Card className="h-full border-dashed">
          <CardContent className="flex flex-col items-center justify-center h-full p-6">
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <Plus className="h-6 w-6 text-primary" />
            </div>
            <p className="font-medium text-center">Create a New Squad</p>
            <p className="text-sm text-muted-foreground text-center mt-1">
              Add your spouse, college, or work friends.
            </p>
            <Link href="/squads/create" className="mt-4">
              <Button variant="outline">Get Started</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
