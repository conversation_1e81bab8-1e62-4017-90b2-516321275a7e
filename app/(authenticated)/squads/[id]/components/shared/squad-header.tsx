"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ArrowLeft, UserPlus, Users } from "lucide-react"
import { Squad } from "@/lib/domains/squad/squad.types"

interface SquadHeaderProps {
  squad: Squad
  onInviteClick?: () => void
  isSquadLead?: boolean
  onRedirectToInvitations?: () => void
}

export function SquadHeader({ squad, onInviteClick, isSquadLead = false }: SquadHeaderProps) {
  // For now, we'll just show placeholder avatars since this component
  // doesn't need the full member details for the header display
  // The actual member management is handled in the members tab

  if (!squad) {
    return (
      <div className="mb-6">
        <div className="relative w-full h-40 sm:h-48 md:h-56 rounded-lg overflow-hidden mb-4 bg-gradient-to-r from-red-500/20 to-red-500/40">
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-center justify-center">
            <p className="text-white">Squad not found</p>
          </div>
        </div>
      </div>
    )
  }

  const memberCount = squad.memberCount || 0

  return (
    <div className="mb-6">
      <div className="relative w-full h-40 sm:h-48 md:h-56 rounded-lg overflow-hidden mb-4 bg-gradient-to-r from-primary/20 to-primary/40">
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent">
          {/* Back button - positioned at top left on mobile with caption */}
          <div className="absolute top-4 left-4 md:hidden z-10">
            <Button
              variant="secondary"
              size="sm"
              className="bg-white/20 hover:bg-white/30 active:bg-white/40 h-8 flex items-center gap-1.5 px-3 text-xs backdrop-blur-sm border border-white/20 shadow-sm transition-all duration-200 hover:shadow-md hover:scale-105 active:scale-95 rounded-full"
              asChild
            >
              <Link href="/dashboard" className="flex items-center">
                <ArrowLeft className="h-3.5 w-3.5 text-white" />
                <span className="text-white">Back</span>
              </Link>
            </Button>
          </div>

          {/* Main content container */}
          <div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6">
            <div className="flex items-start md:items-end">
              {/* Back button - visible only on larger screens */}
              <div className="mr-4 hidden md:block">
                <Button
                  variant="secondary"
                  size="sm"
                  className="bg-white/20 hover:bg-white/30 active:bg-white/40 flex items-center gap-1.5 px-3 backdrop-blur-sm border border-white/20 shadow-sm transition-all duration-200 hover:shadow-md hover:scale-105 active:scale-95 rounded-full"
                  asChild
                >
                  <Link href="/dashboard" className="flex items-center">
                    <ArrowLeft className="h-4 w-4 text-white" />
                    <span className="text-white font-medium">Back to Dashboard</span>
                  </Link>
                </Button>
              </div>

              <div className="flex-1">
                {/* Title and invite button */}
                <div className="flex items-start md:items-center justify-between mb-2 md:mb-1 mt-8 md:mt-0">
                  <h1 className="text-2xl md:text-3xl font-bold text-white mr-2">{squad.name}</h1>
                  {isSquadLead && onInviteClick && (
                    <Button
                      onClick={onInviteClick}
                      size="sm"
                      variant="secondary"
                      className="bg-white/90 hover:bg-white text-primary dark:bg-slate-800 dark:hover:bg-slate-700 dark:text-white shrink-0 border border-transparent"
                    >
                      <UserPlus className="h-3.5 w-3.5 mr-1.5" /> Invite
                    </Button>
                  )}
                </div>

                {/* Squad details - simplified for mobile */}
                <div className="flex items-center gap-2 text-white/90 mt-2">
                  <div className="flex -space-x-2 overflow-hidden">
                    {[...Array(Math.min(3, memberCount))].map((_, i) => (
                      <Avatar key={i} className="border-2 border-background h-6 w-6">
                        <AvatarFallback>{i + 1}</AvatarFallback>
                      </Avatar>
                    ))}
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                    <span className="text-sm sm:text-base">{memberCount} members • Leader</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
