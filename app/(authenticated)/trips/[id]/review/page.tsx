"use client"

import { useEffect, useState } from "react"
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation"
import { <PERSON>Lef<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useUserWithData } from "@/lib/domains/auth/auth.hooks"
import { useRealtimeTrip } from "@/lib/domains/trip/trip.realtime.hooks"
import { useRealtimeUserTripReview } from "@/lib/domains/trip-review/trip-review.realtime.hooks"
import { useLoadUserTripReview } from "@/lib/domains/trip-review/trip-review.hooks"
import { UserTripService } from "@/lib/domains/user-trip/user-trip.service"
import { useSyncTripAttendees } from "@/lib/domains/user-trip/user-trip.hooks"
import { PageLoading } from "@/components/page-loading"
import { TripReviewForm } from "./components/trip-review-form"
import { TripReviewDisplay } from "./components/trip-review-display"

export default function TripReviewPage() {
  const params = useParams()
  const router = useRouter()
  const { user, loading: authLoading } = useUserWithData()
  const tripId = params.id as string

  const { trip, loading: tripLoading, error: tripError } = useRealtimeTrip(tripId)
  const { userReview, loading: reviewLoading } = useRealtimeUserTripReview(tripId)
  const { loadUserReview } = useLoadUserTripReview()
  const { syncAttendees, syncing } = useSyncTripAttendees()

  const [userTripStatus, setUserTripStatus] = useState<any>(null)
  const [statusLoading, setStatusLoading] = useState(true)
  const [attendeesSynced, setAttendeesSynced] = useState(false)

  // Sync trip attendees to fix permissions issue (optional)
  useEffect(() => {
    const syncTripAttendees = async () => {
      if (!tripId || attendeesSynced) return

      try {
        console.log("Syncing trip attendees for permissions...")
        const success = await syncAttendees(tripId)
        if (success) {
          console.log("Trip attendees synced successfully")
          setAttendeesSynced(true)
        } else {
          console.log("Sync failed, but continuing anyway - permissions might work without sync")
          setAttendeesSynced(true) // Mark as "attempted" to avoid retrying
        }
      } catch (error) {
        console.log(
          "Error syncing trip attendees:",
          error instanceof Error ? error.message : String(error)
        )
        console.log("Continuing without sync - permissions might work anyway")
        setAttendeesSynced(true) // Mark as "attempted" to avoid retrying
      }
    }

    syncTripAttendees()
  }, [tripId, syncAttendees, attendeesSynced])

  // Load user trip status
  useEffect(() => {
    const loadUserStatus = async () => {
      if (!user || !tripId) return

      try {
        setStatusLoading(true)
        const status = await UserTripService.checkUserTripStatus(user.uid, tripId)
        setUserTripStatus(status)
      } catch (error) {
        console.error("Error loading user trip status:", error)
      } finally {
        setStatusLoading(false)
      }
    }

    loadUserStatus()
  }, [user, tripId])

  // Load user review if not already loaded
  useEffect(() => {
    if (user && tripId && !userReview && !reviewLoading) {
      loadUserReview(tripId)
    }
  }, [user, tripId, userReview, reviewLoading, loadUserReview])

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push("/login")
    }
  }, [authLoading, user, router])

  // Redirect if trip not found or user not authorized
  useEffect(() => {
    if (!tripLoading && !statusLoading && trip && userTripStatus) {
      // Check if user is a trip attendee
      if (userTripStatus.status !== "going") {
        router.push(`/trips/${tripId}`)
        return
      }

      // Check if trip is completed
      if (trip.status !== "completed") {
        router.push(`/trips/${tripId}`)
        return
      }
    }
  }, [trip, userTripStatus, tripLoading, statusLoading, router, tripId])

  // Show loading state
  if (authLoading || tripLoading || statusLoading || reviewLoading || syncing) {
    return <PageLoading message="Loading trip review..." />
  }

  // Show error state
  if (tripError) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-destructive">Error</h1>
          <p className="text-muted-foreground mt-2">Failed to load trip data</p>
        </div>
      </div>
    )
  }

  // Don't render if we don't have the required data
  if (!user || !trip || !userTripStatus) {
    return null
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="space-y-8">
        {/* Back Button */}
        <div className="flex justify-start">
          <Button
            variant="ghost"
            onClick={() => router.push("/trips")}
            className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Trips
          </Button>
        </div>

        {/* Header with Trip Image */}
        <div className="text-center space-y-6">
          {/* Trip Image */}
          {trip.image && (
            <div className="relative w-full h-48 md:h-64 rounded-xl overflow-hidden shadow-lg mx-auto max-w-2xl">
              <img src={trip.image} alt={trip.destination} className="w-full h-full object-cover" />
              <div className="absolute inset-0 bg-black/20" />
              <div className="absolute bottom-4 left-4 right-4 text-white">
                <h1 className="text-2xl md:text-3xl font-bold drop-shadow-lg">Review Your Trip</h1>
              </div>
            </div>
          )}

          {/* Trip Details */}
          <div className="space-y-2">
            <h2 className="text-xl md:text-2xl font-semibold text-foreground">
              {trip.destination}
            </h2>
            <p className="text-sm text-muted-foreground">
              {trip.startDate.toDate().toLocaleDateString()} -{" "}
              {trip.endDate.toDate().toLocaleDateString()}
            </p>
          </div>

          {/* Fallback header if no image */}
          {!trip.image && (
            <div className="space-y-2">
              <h1 className="text-3xl font-bold">Review Your Trip</h1>
              <h2 className="text-xl text-muted-foreground">{trip.destination}</h2>
              <p className="text-sm text-muted-foreground">
                {trip.startDate.toDate().toLocaleDateString()} -{" "}
                {trip.endDate.toDate().toLocaleDateString()}
              </p>
            </div>
          )}
        </div>

        {/* Review Form or Display */}
        {userReview ? (
          <TripReviewDisplay review={userReview} trip={trip} />
        ) : (
          <TripReviewForm trip={trip} />
        )}
      </div>
    </div>
  )
}
