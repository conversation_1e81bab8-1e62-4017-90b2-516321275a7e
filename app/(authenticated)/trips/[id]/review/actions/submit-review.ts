"use server"

import { revalidatePath } from "next/cache"
import { redirect } from "next/navigation"
import { TripReviewService } from "@/lib/domains/trip-review/trip-review.service"
import { TripService } from "@/lib/domains/trip/trip.service"
import { UserTripService } from "@/lib/domains/user-trip/user-trip.service"
import { TRIP_REVIEW_CONSTRAINTS } from "@/lib/domains/trip-review/trip-review.types"
import { verifyAuthToken } from "@/lib/firebase-admin"

export interface SubmitReviewResult {
  success: boolean
  error?: string
  reviewId?: string
}

export async function submitTripReview(
  tripId: string,
  authToken: string,
  formData: FormData
): Promise<SubmitReviewResult> {
  try {
    // Verify the auth token
    const authResult = await verifyAuthToken(authToken)
    if (!authResult.isValid || !authResult.uid) {
      return {
        success: false,
        error: "You must be logged in to submit a review",
      }
    }

    const userId = authResult.uid

    // Extract form data
    const rating = parseInt(formData.get("rating") as string)
    const feedback = (formData.get("feedback") as string)?.trim()

    // Validate form data
    if (
      !rating ||
      rating < TRIP_REVIEW_CONSTRAINTS.RATING_MIN ||
      rating > TRIP_REVIEW_CONSTRAINTS.RATING_MAX
    ) {
      return {
        success: false,
        error: `Rating must be between ${TRIP_REVIEW_CONSTRAINTS.RATING_MIN} and ${TRIP_REVIEW_CONSTRAINTS.RATING_MAX} stars`,
      }
    }

    if (!feedback || feedback.length < TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MIN_LENGTH) {
      return {
        success: false,
        error: `Feedback must be at least ${TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MIN_LENGTH} characters`,
      }
    }

    if (feedback.length > TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MAX_LENGTH) {
      return {
        success: false,
        error: `Feedback must be no more than ${TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MAX_LENGTH} characters`,
      }
    }

    // Verify trip exists and get trip data
    const trip = await TripService.getTrip(tripId)
    if (!trip) {
      return {
        success: false,
        error: "Trip not found",
      }
    }

    // Verify trip is completed
    if (trip.status !== "completed") {
      return {
        success: false,
        error: "You can only review completed trips",
      }
    }

    // Verify user is a trip attendee
    const userTripStatus = await UserTripService.checkUserTripStatus(userId, tripId)
    if (!userTripStatus || userTripStatus.status !== "going") {
      return {
        success: false,
        error: "You must be a trip attendee to submit a review",
      }
    }

    // Check if user has already reviewed this trip
    const existingReview = await TripReviewService.hasUserReviewed(tripId, userId)
    if (existingReview) {
      return {
        success: false,
        error: "You have already reviewed this trip",
      }
    }

    // Create the review
    const reviewData = {
      tripId,
      userId,
      rating,
      feedback,
      userDisplayName: authResult.email, // Use email as display name fallback
      userPhotoURL: null, // We don't have photo URL from token
    }

    const reviewId = await TripReviewService.createReview(tripId, reviewData)

    // Revalidate relevant pages
    revalidatePath(`/trips/${tripId}`)
    revalidatePath(`/trips/${tripId}/review`)
    revalidatePath("/dashboard")

    return {
      success: true,
      reviewId,
    }
  } catch (error) {
    console.error("Error submitting trip review:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to submit review",
    }
  }
}

export async function redirectToTrip(tripId: string) {
  redirect(`/trips/${tripId}`)
}
