"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent } from "@/components/ui/tabs"
import { Plus } from "lucide-react"
import { ItinerarySuggestions } from "./itinerary-suggestions"
import { Trip } from "@/lib/domains/trip/trip.types"
import { ItineraryItem, ItineraryItemType } from "@/lib/domains/itinerary/itinerary.types"
import {
  useRealtimeItineraryItems,
  useCreateItineraryItem,
  useDeleteItineraryItem,
} from "@/lib/domains/itinerary/itinerary.hooks"
import { ActivityDialog } from "./activity-dialog"
import { ItineraryDayTabs } from "./itinerary-day-tabs"
import { ItineraryActivityList } from "./itinerary-activity-list"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { useToast } from "@/components/ui/use-toast"
import { ItinerarySuggestion } from "@/lib/openai"
import { Timestamp } from "firebase/firestore"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

interface TripItineraryTabProps {
  trip: Trip
  isActive?: boolean
}

export function TripItineraryTab({ trip, isActive = true }: TripItineraryTabProps) {
  const user = useUser()
  const { toast } = useToast()
  const [activeDay, setActiveDay] = useState("1")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [activityToEdit, setActivityToEdit] = useState<ItineraryItem | undefined>(undefined)
  const [activityToDelete, setActivityToDelete] = useState<ItineraryItem | undefined>(undefined)
  const [itineraryItems, setItineraryItems] = useState<ItineraryItem[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Check if trip is completed
  const isTripCompleted = trip.status === "completed"

  // Generate dates for the trip
  const dates = []
  if (trip.startDate && trip.endDate) {
    const start = trip.startDate.toDate()
    const end = trip.endDate.toDate()
    const dayCount = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1

    for (let i = 0; i < dayCount; i++) {
      const date = new Date(start)
      date.setDate(date.getDate() + i)
      dates.push(date)
    }
  }

  // Load itinerary items with real-time updates (only when active)
  const { items, loading, error } = useRealtimeItineraryItems(isActive ? trip.id : "")

  // Update local state when items change
  useEffect(() => {
    if (items) {
      setItineraryItems(items)
      setIsLoading(loading)
    }
  }, [items, loading])

  // Handle errors
  useEffect(() => {
    if (error) {
      console.error("Error loading itinerary items:", error)
      toast({
        title: "Error",
        description: "Failed to load itinerary items. Please try again.",
        variant: "destructive",
      })
    }
  }, [error, toast])

  // Use the create itinerary item hook
  const { create: createItineraryItem } = useCreateItineraryItem()

  // Handle adding a new activity from AI suggestions
  const handleAddFromSuggestion = async (suggestion: ItinerarySuggestion) => {
    if (!user) return

    // Convert timeOfDay to hours and create start/end timestamps
    let startHour = 12
    if (suggestion.timeOfDay === "morning") startHour = 9
    if (suggestion.timeOfDay === "afternoon") startHour = 14
    if (suggestion.timeOfDay === "evening") startHour = 19

    // Calculate the date for the currently selected day (not the suggestion's original day)
    const currentDayIndex = parseInt(activeDay) - 1
    const startDate = new Date(trip.startDate.toDate())
    startDate.setDate(startDate.getDate() + currentDayIndex)
    startDate.setHours(startHour, 0, 0, 0)

    // End time is 2 hours after start time
    const endDate = new Date(startDate)
    endDate.setHours(startHour + 2, 0, 0, 0)

    const newActivity = {
      tripId: trip.id,
      title: suggestion.title,
      description: suggestion.description,
      type: "activity" as ItineraryItemType,
      startTime: Timestamp.fromDate(startDate),
      endTime: Timestamp.fromDate(endDate),
      location: "",
      isBooked: false,
    }

    try {
      // Create the activity using the hook
      const itemId = await createItineraryItem(newActivity)

      if (!itemId) {
        toast({
          title: "Error",
          description: "Failed to add activity. Please try again.",
          variant: "destructive",
        })
      }
      // Toast notification is handled by the hook
    } catch (error) {
      console.error("Error adding activity from suggestion:", error)
      // Error toast is handled by the hook
    }
  }

  // Handle editing an activity
  const handleEditActivity = (activity: ItineraryItem) => {
    setActivityToEdit(activity)
    setIsEditDialogOpen(true)
  }

  // Handle deleting an activity
  const handleDeleteActivity = (activity: ItineraryItem) => {
    setActivityToDelete(activity)
    setIsDeleteDialogOpen(true)
  }

  // Use the delete itinerary item hook
  const { remove: deleteItineraryItem } = useDeleteItineraryItem()

  // Confirm deletion of an activity
  const confirmDeleteActivity = async () => {
    if (!activityToDelete) return

    try {
      const success = await deleteItineraryItem(activityToDelete.id)

      if (!success) {
        toast({
          title: "Error",
          description: "Failed to delete activity. Please try again.",
          variant: "destructive",
        })
      }
      // Success toast is handled by the hook
    } catch (error) {
      console.error("Error deleting activity:", error)
      // Error toast is handled by the hook
    } finally {
      setIsDeleteDialogOpen(false)
      setActivityToDelete(undefined)
    }
  }

  // Handle activity dialog success
  const handleActivityDialogSuccess = async () => {
    // No need to refresh manually as we're using real-time updates
    toast({
      title: "Success",
      description: "Itinerary updated successfully",
    })
  }

  // Handle day change
  const handleDayChange = (value: string) => {
    setActiveDay(value.replace("day-", ""))
  }

  return (
    <div className="space-y-6 max-w-full overflow-hidden">
      {/* Show completion notice for completed trips */}
      {isTripCompleted && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <span className="text-green-600 font-medium">✅ Trip Completed</span>
          </div>
          <p className="text-green-700 text-sm mt-1">
            This trip has been completed. The itinerary is now read-only for historical reference.
          </p>
        </div>
      )}

      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h2 className="text-2xl font-bold">Trip Itinerary</h2>
        {!isTripCompleted && (
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Add Activity for Day {activeDay}
          </Button>
        )}
      </div>

      {dates.length > 0 ? (
        <Tabs
          defaultValue={`day-1`}
          value={`day-${activeDay}`}
          onValueChange={handleDayChange}
          className="space-y-4 w-full max-w-full"
        >
          <ItineraryDayTabs
            dates={dates}
            activeDay={activeDay}
            itineraryItems={itineraryItems}
            onDayChange={handleDayChange}
          />

          {dates.map((date, dayIndex) => {
            const dayNumber = dayIndex + 1

            // Filter activities for this day based on startTime
            const startOfDay = new Date(date)
            startOfDay.setHours(0, 0, 0, 0)

            const endOfDay = new Date(date)
            endOfDay.setHours(23, 59, 59, 999)

            const dayActivities = itineraryItems.filter((item) => {
              if (!item.startTime) return false
              const itemDate = item.startTime.toDate()
              return itemDate >= startOfDay && itemDate <= endOfDay
            })

            return (
              <TabsContent
                key={dayIndex}
                value={`day-${dayNumber}`}
                className="space-y-4 w-full max-w-full overflow-x-hidden"
              >
                <ItineraryActivityList
                  date={date}
                  dayNumber={dayNumber}
                  activities={dayActivities}
                  onAddActivity={() => setIsAddDialogOpen(true)}
                  onEditActivity={handleEditActivity}
                  onDeleteActivity={handleDeleteActivity}
                  isTripCompleted={isTripCompleted}
                />

                {!isLoading && !isTripCompleted && (
                  <ItinerarySuggestions
                    trip={trip}
                    day={dayNumber}
                    existingActivities={dayActivities.map((item) => ({
                      ...item,
                      time: item.startTime.toDate().toTimeString().substring(0, 5),
                      day: dayNumber,
                    }))}
                    onActivitySelected={handleAddFromSuggestion}
                  />
                )}
              </TabsContent>
            )
          })}
        </Tabs>
      ) : (
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-muted-foreground mb-4">
              No dates set for this trip. Please update the trip details with start and end dates.
            </p>
            <Button variant="outline">Edit Trip Details</Button>
          </CardContent>
        </Card>
      )}

      {/* Add Activity Dialog */}
      <ActivityDialog
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        trip={trip}
        day={parseInt(activeDay)}
        userId={user?.uid || ""}
        onSuccess={handleActivityDialogSuccess}
      />

      {/* Edit Activity Dialog */}
      {activityToEdit && (
        <ActivityDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          trip={trip}
          day={parseInt(activeDay)} /* Use current active day instead */
          userId={user?.uid || ""}
          activity={activityToEdit}
          onSuccess={handleActivityDialogSuccess}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Activity</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this activity? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteActivity}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
